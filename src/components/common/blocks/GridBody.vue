<template>
  <div class="q-ml-md">
    <div class="row items-start q-mt-sm" style="min-width: 800px">
      <!-- Row section (sub-questions) -->
      <div class="col align-top fixed-height">
        <q-item-label class="q-mr-sm q-mb-sm">แถว (คำถามย่อย)</q-item-label>
        <div
          v-for="(question, index) in store.gridRowQuestions"
          :key="index"
          class="row items-center q-mb-sm draggable-row"
          @dragover.prevent
          @drop="store.gridDrop(index, $event, 'row')"
        >
          <q-btn
            padding="xs"
            flat
            round
            icon="drag_indicator"
            color="grey"
            @mousedown="store.startDrag(index, 'row')"
            draggable="true"
            @dragstart="store.handleDragStart($event)"
            @dragend="store.endDrag"
            @mouseover="store.hoverRow(index)"
            style="cursor: move"
          />
          <q-item-label class="q-pr-md q-pl-sm">{{ index + 1 }}.</q-item-label>
          <div class="column">
            <div class="row items-center">
              <q-input
                v-model="store.gridRowQuestions[index]!.label"
                @blur="debouncedQuestionUpdate(index)"
                dense
                class="sub-question-input"
                placeholder="คำถามย่อย"
              ></q-input>
            </div>
          </div>
          <q-btn
            v-if="store.gridRowQuestions?.length > 1"
            flat
            round
            :icon="isDeletingQuestion ? 'hourglass_empty' : 'close'"
            :loading="isDeletingQuestion"
            :disable="isDeletingQuestion"
            class="q-ml-sm"
            @click="safeRemoveRowQuestion(index)"
          />
        </div>
        <q-btn
          padding="xs"
          flat
          color="primary"
          label="เพิ่มคำถามย่อย"
          icon="add"
          class="q-mt-sm"
          @click="safeAddRowQuestion()"
        />
      </div>
      <!-- Column section (shared options) -->
      <div class="col align-top fixed-height">
        <q-item-label class="q-mr-sm q-mb-sm">คอลัมน์ (ตัวเลือก)</q-item-label>

        <div class="scroll-container">
          <div class="row q-col-gutter-md">
            <div
              v-for="(_, index) in store.gridColumnOptions"
              :key="index"
              class="q-pa-sm bordered-box"
              style="min-width: 400px"
              @dragover.prevent
              @drop="store.gridDrop(index, $event, 'col')"
            >
              <div class="row items-center q-gutter-sm">
                <!-- Drag handle -->
                <q-btn
                  padding="xs"
                  flat
                  round
                  icon="drag_indicator"
                  color="grey"
                  @mousedown="store.startDrag(index, 'col')"
                  draggable="true"
                  @dragstart="store.handleDragStart($event)"
                  @dragend="store.endDrag"
                  @mouseover="store.hoverRow(index)"
                  style="cursor: move"
                />
                <!-- Radio -->
                <q-radio
                  v-model="store.gridColumnOptions[index]!.value"
                  val="columnChoices"
                  disable
                  class="no-padding"
                />

                <!-- Input field -->
                <q-input
                  v-model="store.gridColumnOptions[index]!.optionText"
                  placeholder="ข้อความตัวเลือก"
                  @blur="debouncedOptionUpdate(index, 'optionText')"
                  dense
                  class="option-text-input"
                  style="flex: 1"
                />

                <!-- Score field (if quiz) -->
                <q-input
                  v-if="props.type === 'quiz'"
                  v-model.number="store.gridColumnOptions[index]!.score"
                  type="number"
                  dense
                  class="value-input"
                  style="width: 100px"
                  @blur="debouncedOptionUpdate(index, 'score')"
                />

                <!-- Delete button -->
                <q-btn
                  v-if="store.gridColumnOptions.length > 1"
                  flat
                  round
                  :icon="isDeletingOption ? 'hourglass_empty' : 'close'"
                  :loading="isDeletingOption"
                  :disable="isDeletingOption"
                  @click="safeRemoveColumnOption(index)"
                />
              </div>
            </div>
          </div>
        </div>

        <q-btn
          padding="xs"
          flat
          color="primary"
          label="เพิ่มตัวเลือกร่วม"
          icon="add"
          class="q-mt-sm"
          @click="safeAddColumnOption()"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref, watch, onMounted, onUnmounted } from 'vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock, Question, Option } from 'src/types/models';
import { OptionService } from 'src/services/asm/optionService';
import questionService from 'src/services/asm/questionService';

// Get the store instance provided by the parent ItemBlock
const store = inject<ItemBlockStore>('blockStore');
if (!store) {
  throw new Error('GridBody must be used within an ItemBlock component');
}

// Get the itemBlock from parent component
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
}>();

// Emit events to parent component
const emit = defineEmits<{
  'update:question': [
    data: {
      questionId?: number;
      questionText?: string;
      itemBlockId: number;
      updatedQuestion?: Question;
      action: 'created' | 'updated' | 'deleted';
    },
  ];
  'update:option': [
    data: {
      action: 'created' | 'updated' | 'deleted';
      itemBlockId: number;
      option?: Option;
      optionId?: number;
      updateData?: { index: number; option: Option };
    },
  ];
}>();

// Services
const optionService = new OptionService();

// Create a reactive array to hold preview selections
// This is just for UI preview and doesn't affect actual data
const previewSelection = ref<number[]>([]);

// Loading states
const isCreatingQuestion = ref(false);
const isCreatingOption = ref(false);
const isUpdatingQuestion = ref(false);
const isUpdatingOption = ref(false);
const isDeletingQuestion = ref(false);
const isDeletingOption = ref(false);

// Initialize preview selection array when questions change
watch(
  () => store.gridRowQuestions?.length,
  (newLength) => {
    // Reset the preview selection array with the correct length
    previewSelection.value = Array(newLength).fill(null);
  },
  { immediate: true },
);

// Initialize grid data from existing itemBlock data
onMounted(() => {
  initializeGridData();
});

// Initialize grid data from backend
function initializeGridData() {
  if (!props.itemBlock) return;

  // Initialize questions (excluding header questions)
  const nonHeaderQuestions = props.itemBlock.questions?.filter((q) => !q.isHeader) || [];
  if (nonHeaderQuestions.length > 0) {
    store!.gridRowQuestions = nonHeaderQuestions.map((question, index) => ({
      id: question.id,
      label: question.questionText || '',
      value: `question${index + 1}`,
      sequence: question.sequence || index + 1,
    }));
  }

  // Initialize shared options
  const options = props.itemBlock.options || [];
  if (options.length > 0) {
    store!.gridColumnOptions = options.map((option, index) => ({
      id: option.id,
      label: option.optionText || `ตัวเลือกที่ ${index + 1}`,
      value: `option${index + 1}`,
      optionText: option.optionText || '',
      score: option.value || 0,
      sequence: option.sequence || index + 1,
    }));
  }
}

// Enhanced function to add row question with backend sync
async function addRowQuestionWithSync() {
  if (isCreatingQuestion.value) return;

  try {
    isCreatingQuestion.value = true;

    // Add to local store first using the original method
    const newIndex = store!.gridRowQuestions.length + 1;
    store!.gridRowQuestions.push({
      label: ``, // Empty by default, only show placeholder
      value: `question${newIndex}`,
      sequence: newIndex,
    });

    const newQuestionIndex = store!.gridRowQuestions.length - 1;
    const newQuestion = store!.gridRowQuestions[newQuestionIndex];

    if (!newQuestion) return;

    // Create question in backend with isHeader: false
    const questionData = {
      itemBlockId: props.itemBlock.id,
      questionText: newQuestion.label || '',
      isHeader: false, // Required: All GRID questions must have isHeader: false
      sequence: newQuestion.sequence,
    };

    const createdQuestion = await questionService.createQuestion(questionData);

    if (createdQuestion?.data) {
      // Update local store with backend ID
      store!.gridRowQuestions[newQuestionIndex] = {
        ...newQuestion,
        id: createdQuestion.data.id,
        label: createdQuestion.data.questionText,
      };

      // Emit to parent component
      emit('update:question', {
        questionId: createdQuestion.data.id,
        questionText: createdQuestion.data.questionText,
        itemBlockId: props.itemBlock.id,
        updatedQuestion: createdQuestion.data,
        action: 'created',
      });
    }
  } catch (error) {
    console.error('❌ Failed to create GRID question:', error);

    // Remove from local store if backend creation failed
    if (store!.gridRowQuestions.length > 0) {
      store!.gridRowQuestions.splice(store!.gridRowQuestions.length - 1, 1);
    }
  } finally {
    isCreatingQuestion.value = false;
  }
}

// Enhanced function to add column option with backend sync
async function addColumnOptionWithSync() {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // Log context information
    console.log('🎯 [GRID] Adding new shared column option for ItemBlock:', {
      itemBlockId: props.itemBlock.id,
      assessmentId: props.itemBlock.assessmentId,
      sequence: props.itemBlock.sequence,
      section: props.itemBlock.section,
      currentOptionsCount: store!.gridColumnOptions.length,
      currentQuestionsCount: store!.gridRowQuestions.length,
    });

    // Add to local store first using direct manipulation
    const newIndex = store!.gridColumnOptions.length + 1;
    store!.gridColumnOptions.push({
      label: '',
      value: `option${newIndex}`,
      optionText: '', // Empty by default, only show placeholder
      score: 0, // Default score value
      sequence: newIndex,
    });

    const newOptionIndex = store!.gridColumnOptions.length - 1;
    const newOption = store!.gridColumnOptions[newOptionIndex];

    if (!newOption) return;

    // Create shared option in backend
    const optionData = {
      itemBlockId: props.itemBlock.id,
      optionText: newOption.optionText || '',
      value: newOption.score || 0,
      sequence: newOption.sequence,
    };

    console.log(
      '📤 [GRID] Sending shared column option creation request:',
      JSON.stringify(optionData, null, 2),
    );

    const createdOption = await optionService.createOption(optionData);

    // Log successful creation with full context
    console.log(
      '✅ [GRID] Shared column option created successfully:',
      JSON.stringify(
        {
          createdOption,
          context: {
            itemBlockId: props.itemBlock.id,
            assessmentId: props.itemBlock.assessmentId,
            sequence: props.itemBlock.sequence,
            section: props.itemBlock.section,
            newOptionIndex,
            totalOptionsAfterCreation: store!.gridColumnOptions.length,
            totalQuestions: store!.gridRowQuestions.length,
          },
        },
        null,
        2,
      ),
    );

    if (createdOption) {
      // Update local store with backend ID
      store!.gridColumnOptions[newOptionIndex] = {
        ...newOption,
        id: createdOption.id,
        optionText: createdOption.optionText,
        score: createdOption.value,
      };

      // Emit to parent component
      emit('update:option', {
        action: 'created',
        itemBlockId: props.itemBlock.id,
        option: createdOption,
      });

      console.log(
        '📡 [GRID] Emitted update:option event with data:',
        JSON.stringify(
          {
            action: 'created',
            itemBlockId: props.itemBlock.id,
            optionId: createdOption.id,
            optionText: createdOption.optionText,
            value: createdOption.value,
            sequence: createdOption.sequence,
          },
          null,
          2,
        ),
      );
    }
  } catch (error) {
    console.error('❌ [GRID] Failed to create GRID shared option:', error);
    console.error('❌ [GRID] Context during failure:', {
      itemBlockId: props.itemBlock.id,
      assessmentId: props.itemBlock.assessmentId,
      optionsCount: store!.gridColumnOptions.length,
      questionsCount: store!.gridRowQuestions.length,
    });

    // Remove from local store if backend creation failed
    if (store!.gridColumnOptions.length > 0) {
      store!.gridColumnOptions.splice(store!.gridColumnOptions.length - 1, 1);
    }
  } finally {
    isCreatingOption.value = false;
  }
}

// Enhanced function to update row question with backend sync
async function updateRowQuestionWithSync(index: number) {
  if (isUpdatingQuestion.value) return;

  try {
    isUpdatingQuestion.value = true;

    const question = store!.gridRowQuestions[index];
    if (!question || !question.id) return;

    // Update in backend
    const updateData = {
      questionText: question.label,
      itemBlockId: props.itemBlock.id,
      isHeader: false, // Ensure isHeader remains false
    };

    const updatedQuestion = await questionService.updateQuestion(question.id, updateData);

    if (updatedQuestion) {
      // Emit to parent component
      emit('update:question', {
        questionId: question.id,
        questionText: updatedQuestion.questionText,
        itemBlockId: props.itemBlock.id,
        updatedQuestion: updatedQuestion,
        action: 'updated',
      });
    }
  } catch (error) {
    console.error('❌ Failed to update GRID question:', error);
  } finally {
    isUpdatingQuestion.value = false;
  }
}

// Enhanced function to update column option with backend sync
async function updateColumnOptionWithSync(index: number) {
  if (isUpdatingOption.value) return;

  try {
    isUpdatingOption.value = true;

    const option = store!.gridColumnOptions[index];
    if (!option || !option.id) return;

    // ✅ CRITICAL FIX: Create minimal payload that excludes imagePath for text/value updates
    // This prevents accidentally clearing imagePath when updating optionText or value
    const updateData = {
      itemBlockId: props.itemBlock.id,
      optionText: option.optionText,
      value: option.score,
      // ✅ CRITICAL: Do NOT include imagePath in GRID option updates
      // This allows backend to preserve existing imagePath values
    };

    const updatedOption = await optionService.updateOption(option.id, updateData);

    if (updatedOption) {
      // Update local store
      store!.gridColumnOptions[index] = {
        ...option,
        optionText: updatedOption.optionText,
        score: updatedOption.value,
      };

      // Emit to parent component
      emit('update:option', {
        action: 'updated',
        itemBlockId: props.itemBlock.id,
        optionId: option.id,
        updateData: { index, option: updatedOption },
      });
    }
  } catch (error) {
    console.error('❌ Failed to update GRID shared option:', error);
  } finally {
    isUpdatingOption.value = false;
  }
}

// Enhanced function to delete row question with backend sync
async function deleteRowQuestionWithSync(index: number) {
  if (isDeletingQuestion.value) return;

  // Double-check minimum requirement
  if (store!.gridRowQuestions.length <= 1) {
    return;
  }

  try {
    isDeletingQuestion.value = true;

    const question = store!.gridRowQuestions[index];
    if (!question) {
      console.warn('Question not found at index:', index);
      return;
    }

    // If question has an ID, delete from backend first
    if (question.id) {
      await questionService.deleteQuestion(0, question.id); // quizId not needed for evaluation questions

      // Emit to parent component
      emit('update:question', {
        questionId: question.id,
        itemBlockId: props.itemBlock.id,
        action: 'deleted',
      });
    }

    // Remove from local store using the original store method
    store!.removeRowQuestion(index);

    // Update preview selection array to match new length
    previewSelection.value = Array(store!.gridRowQuestions.length).fill(null);
  } catch (error) {
    console.error('❌ Failed to delete GRID question:', error);
  } finally {
    isDeletingQuestion.value = false;
  }
}

// Enhanced function to delete column option with backend sync
async function deleteColumnOptionWithSync(index: number) {
  if (isDeletingOption.value) return;

  // Double-check minimum requirement
  if (store!.gridColumnOptions.length <= 1) {
    return;
  }

  try {
    isDeletingOption.value = true;

    const option = store!.gridColumnOptions[index];
    if (!option) {
      console.warn('Option not found at index:', index);
      return;
    }

    // If option has an ID, delete from backend first
    if (option.id) {
      await optionService.removeOption(option.id);

      // Emit to parent component
      emit('update:option', {
        action: 'deleted',
        itemBlockId: props.itemBlock.id,
        optionId: option.id,
      });
    }

    // Remove from local store using the original store method
    store!.removeColumnOption(index);
  } catch (error) {
    console.error('❌ Failed to delete GRID shared option:', error);
  } finally {
    isDeletingOption.value = false;
  }
}

// Safe wrapper functions that handle both local state and backend sync
async function safeAddRowQuestion() {
  await addRowQuestionWithSync();
}

async function safeAddColumnOption() {
  await addColumnOptionWithSync();
}

async function safeRemoveRowQuestion(index: number) {
  // Prevent deletion if only one question remains
  if (store!.gridRowQuestions.length <= 1) {
    return;
  }

  // Delete immediately without confirmation
  await deleteRowQuestionWithSync(index);
}

async function safeRemoveColumnOption(index: number) {
  // Prevent deletion if only one option remains
  if (store!.gridColumnOptions.length <= 1) {
    return;
  }

  // Delete immediately without confirmation
  await deleteColumnOptionWithSync(index);
}

// Debounced update functions for auto-save
const debounceTimeouts = ref<Map<string, number>>(new Map());

function debouncedQuestionUpdate(index: number) {
  const key = `question-${index}`;

  // Clear existing timeout
  if (debounceTimeouts.value.has(key)) {
    clearTimeout(debounceTimeouts.value.get(key));
  }

  // Set new timeout - use non-async callback to avoid Promise return warning
  const timeoutId = window.setTimeout(() => {
    const question = store!.gridRowQuestions[index];
    if (question && question.id) {
      // Call async function but don't await in setTimeout callback
      updateRowQuestionWithSync(index).catch((error) => {
        console.error('Failed to update question:', error);
      });
    }
    debounceTimeouts.value.delete(key);
  }, 800); // 800ms debounce

  debounceTimeouts.value.set(key, timeoutId);
}

function debouncedOptionUpdate(index: number, field: 'optionText' | 'score') {
  const key = `option-${index}-${field}`;

  // Clear existing timeout
  if (debounceTimeouts.value.has(key)) {
    clearTimeout(debounceTimeouts.value.get(key));
  }

  // Set new timeout - use non-async callback to avoid Promise return warning
  const timeoutId = window.setTimeout(() => {
    const option = store!.gridColumnOptions[index];
    if (option && option.id) {
      // Call async function but don't await in setTimeout callback
      updateColumnOptionWithSync(index).catch((error) => {
        console.error('Failed to update option:', error);
      });
    }
    debounceTimeouts.value.delete(key);
  }, 800); // 800ms debounce

  debounceTimeouts.value.set(key, timeoutId);
}

// Cleanup timeouts on component unmount
onUnmounted(() => {
  debounceTimeouts.value.forEach((timeoutId) => {
    clearTimeout(timeoutId);
  });
  debounceTimeouts.value.clear();
});
</script>
<style scoped>
.q-input {
  max-width: 400px;
  transition: all 0.3s ease;
}

.draggable-row {
  transition: all 0.3s ease;
}

.draggable-row:hover,
.draggable-row[dragged-index]:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.draggable-row.dragging {
  opacity: 0.5;
}

.align-top {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: 100%;
}

.fixed-height {
  min-height: 100px; /* กำหนดความสูงขั้นต่ำเพื่อให้ไม่ลอย */
  height: 100%;
}

.row.items-start {
  align-items: flex-start;
}

.q-ml-md {
  display: grid;
  grid-template-columns: 1fr 1fr; /* ให้แต่ละฝั่งมีขนาดเท่ากัน */
  gap: 20px;
}

.sub-question-input {
  border-left: 3px solid #9e9e9e; /* Grey border to indicate sub-question */
  background-color: rgba(158, 158, 158, 0.05); /* Light grey background */
}

/* Grid preview styles */
.grid-preview {
  margin-top: 2rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  background-color: #f9f9f9;
}

.grid-table {
  overflow-x: auto;
}

.q-table {
  width: 100%;
  border-collapse: collapse;
}

.q-table th,
.q-table td {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
}

.q-table th {
  background-color: #f5f5f5;
  font-weight: 500;
}

.option-text-input {
  width: 100%;
}

.value-input {
  width: 100%;
  max-width: 120px;
}

.option-value {
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
}

.full-width {
  width: 100%;
}

.scroll-container {
  overflow-x: auto;
  white-space: nowrap;
}
</style>
