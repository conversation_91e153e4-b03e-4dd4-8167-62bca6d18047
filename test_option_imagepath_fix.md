# Option ImagePath Bug Fix Test

## Bug Description
When updating optionText in option components, the imagePath was being incorrectly set to null, causing existing option images to disappear.

## Root Cause
1. **Frontend Issue**: The `performOptionSave` function in `ItemBlockComponent.vue` was including `imagePath` in the update payload even when only updating text, sometimes sending `null` or empty values.

2. **Backend Issue**: The `update` method in `options.service.ts` was not properly handling the case where `imagePath` is not provided in the update payload.

## Fix Applied

### Frontend Changes (`src/components/common/blocks/ItemBlockComponent.vue`)
- Modified `performOptionSave` to exclude `imagePath` from the payload when updating optionText or value
- This prevents accidentally sending `null` or empty `imagePath` values to the backend

### Backend Changes (`src/resources/assessment-forms/options/options.service.ts`)
- Enhanced the `update` and `updateForQuestion` methods to properly handle `imagePath` preservation
- Added logic to preserve existing `imagePath` when it's not provided in the update payload
- Only update `imagePath` when it's explicitly provided or there's a file upload

### Grid Component Fix (`src/components/common/blocks/GridBody.vue`)
- Added comment to clarify that `imagePath` is intentionally excluded from GRID option updates

## Test Scenario
1. Create an option with both text and image
2. Update the option text
3. Verify that the image remains visible and `imagePath` is preserved

## Expected Behavior After Fix
- Option text updates should not affect existing images
- Option images should remain visible after text updates
- `imagePath` values should be preserved in the database
- Only explicit image operations should modify `imagePath`

## Files Modified
- `src/components/common/blocks/ItemBlockComponent.vue`
- `src/components/common/blocks/GridBody.vue`
- `src/resources/assessment-forms/options/options.service.ts`
